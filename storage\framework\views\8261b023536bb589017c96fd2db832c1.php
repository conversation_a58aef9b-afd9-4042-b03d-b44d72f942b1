<div class="bg-white rounded-lg border border-custom-second-darkest border-opacity-20">
    <!-- Header -->
    <div class="flex items-center justify-between px-4 py-4 border-b border-gray-100">
        <h3 class="text-lg font-bold text-custom-lightest bg-custom-green text-custom-darkest px-4 py-2 rounded -mx-4 -my-4 mb-0">
            Suggested Connections
        </h3>
        <button 
            wire:click="refreshSuggestions" 
            wire:loading.attr="disabled"
            class="text-gray-500 hover:text-gray-700 transition-colors p-1 rounded-full hover:bg-gray-100"
            title="Refresh suggestions">
            <svg wire:loading.remove wire:target="refreshSuggestions" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <svg wire:loading wire:target="refreshSuggestions" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        </button>
    </div>

    <!-- Content -->
    <div class="p-4">
        <!--[if BLOCK]><![endif]--><?php if($isLoading): ?>
            <!-- Loading State -->
            <div class="space-y-4">
                <!--[if BLOCK]><![endif]--><?php for($i = 0; $i < 3; $i++): ?>
                    <div class="animate-pulse">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gray-200 rounded-full"></div>
                            <div class="flex-1 space-y-2">
                                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                            </div>
                            <div class="w-16 h-8 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php elseif(empty($suggestedUsers)): ?>
            <!-- Empty State -->
            <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p class="text-gray-500 text-sm">No suggestions available</p>
                <p class="text-gray-400 text-xs mt-1">Complete your profile to get better suggestions</p>
            </div>
        <?php else: ?>
            <!-- Suggested Users -->
            <div class="space-y-4">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $suggestedUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $suggestion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                        <!-- User Avatar -->
                        <a href="<?php echo e(route('profile.user', $suggestion['user'])); ?>" class="flex-shrink-0">
                            <img class="w-12 h-12 rounded-full object-cover"
                                 src="<?php echo e($suggestion['user']->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($suggestion['user']->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($suggestion['user']->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                 alt="<?php echo e($suggestion['user']->name); ?>">
                        </a>

                        <!-- User Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <a href="<?php echo e(route('profile.user', $suggestion['user'])); ?>" 
                                   class="font-medium text-gray-900 hover:text-custom-green transition-colors truncate">
                                    <?php echo e($suggestion['user']->name); ?>

                                </a>
                            </div>
                            
                            <!-- User Role/Status -->
                            <p class="text-xs text-gray-500 mb-1">
                                <?php echo e($suggestion['user']->student_id ? $suggestion['user']->student_id . ' • ' : ''); ?>

                                <?php echo e(ucfirst(str_replace('_', ' ', $suggestion['user']->role))); ?>

                            </p>

                            <!-- Shared Attributes -->
                            <!--[if BLOCK]><![endif]--><?php if(!empty($suggestion['shared_attributes'])): ?>
                                <div class="space-y-1">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = array_slice($suggestion['shared_attributes'], 0, 2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <p class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full inline-block mr-1">
                                            <?php echo e($attribute); ?>

                                        </p>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                            <!-- Follow Button -->
                            <div class="mt-2">
                                <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
                                    <!--[if BLOCK]><![endif]--><?php if(auth()->id() !== $suggestion['user']->id): ?>
                                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user-follower', ['user' => $suggestion['user'],'compact' => true]);

$__html = app('livewire')->mount($__name, $__params, 'suggested-' . $suggestion['user']->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php else: ?>
                                    <a href="<?php echo e(route('login')); ?>" 
                                       class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors">
                                        Follow
                                    </a>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- View All Link -->
            <div class="mt-4 pt-3 border-t border-gray-100">
                <a href="<?php echo e(route('follow-management.following', ['tab' => 'discover'])); ?>" 
                   class="text-sm text-custom-green hover:text-custom-second-darkest font-medium flex items-center justify-center">
                    <span>Discover More People</span>
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/suggested-connections.blade.php ENDPATH**/ ?>